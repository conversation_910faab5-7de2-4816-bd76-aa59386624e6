import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream/promises';

config();

const supabase = createClient(
  process.env.SUPABASE_PROJECT_URL,
  process.env.SUPABASE_ADMIN_KEY
);

const PIXELDRAIN_API_KEY = process.env.PIXELDRAIN_API_KEY;
const ANILIST_API = 'https://graphql.anilist.co';
const MIN_DELAY = 5000;
const MAX_DELAY = 5000;

function isEnglishOnly(text) {
  if (/[^\x00-\x7F]/.test(text)) {
    return false;
  }
  return /^[a-zA-Z0-9\s\-_.]+$/.test(text);
}

const getRandomDelay = () =>
  Math.floor(Math.random() * (MAX_DELAY - MIN_DELAY + 1) + MIN_DELAY);

async function downloadImage(url, tempPath) {
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`Failed to download: ${response.statusText}`);
    await pipeline(response.body, fs.createWriteStream(tempPath));
    return true;
  } catch (error) {
    console.error(`Error downloading image: ${error}`);
    return false;
  }
}

async function uploadToPixeldrain(filePath) {
  let attempts = 0;
  const maxAttempts = 3;
  let fileStream = null;

  while (attempts < maxAttempts) {
    attempts++;
    try {
      const fileName = path.basename(filePath);
      const apiUrl = `https://pixeldrain.com/api/file/${fileName}`;
      fileStream = fs.createReadStream(filePath);

      console.log(`Attempting to upload ${fileName} to Pixeldrain (attempt ${attempts})`);

      const response = await fetch(apiUrl, {
        method: 'PUT',
        headers: {
          'Authorization': `Basic ${Buffer.from(`:${PIXELDRAIN_API_KEY}`).toString('base64')}`
        },
        body: fileStream
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${errorText}`);
      }

      const data = await response.json();
      console.log(`Successfully uploaded ${fileName} to Pixeldrain`);
      return `https://pixeldrain.com/api/file/${data.id}`;
    } catch (error) {
      console.error(`Upload attempt ${attempts} failed:`, error);
      if (attempts >= maxAttempts) throw error;
      await new Promise(resolve => setTimeout(resolve, 60000));
    } finally {
      if (fileStream) {
        fileStream.destroy();
      }
    }
  }
}

const metadataQuery = `
query ($id: Int) {
  Media(id: $id, type: ANIME) {
    id
    idMal
    synonyms
    title {
      romaji
      english
      native
    }
    coverImage {
      large
      extraLarge
    }
    bannerImage
    format
    episodes
    duration
    source
    season
    seasonYear
    startDate {
      year
      month
      day
    }
    endDate {
      year
      month
      day
    }
    studios(isMain: true) {
      nodes {
        name
      }
    }
    genres
    relations {
      edges {
        id
        relationType
        node {
          id
          type
          format
          status
        }
      }
    }
  }
}`;

const fetchAnimeMetadata = async (anilistId) => {
  try {
    const { data: existingMetadata, error: metadataError } = await supabase
      .from('anime_metadata')
      .select('banner_image_manual_override, banner_image, english_title')
      .eq('anilist_id', anilistId)
      .single();

    if (metadataError && metadataError.code !== 'PGRST116') {
      console.error(`Error checking existing metadata for ID ${anilistId}:`, metadataError);
    }

    const response = await fetch(ANILIST_API, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: metadataQuery,
        variables: { id: anilistId },
      }),
    });

    const { data } = await response.json();
    if (!data?.Media) return null;

    const media = data.Media;

    let coverImageUrl = media.coverImage?.large;
    coverImageUrl = coverImageUrl.replace('/medium/', '/large/')
    let bannerImageUrl = media.bannerImage;
    const tempDir = path.join(process.cwd(), 'temp');

    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir);
    }

    if (coverImageUrl?.includes('s4.anilist')) {
      const tempCoverPath = path.join(tempDir, `cover_${anilistId}.jpg`);
      let downloadSuccess = await downloadImage(coverImageUrl, tempCoverPath);

      // If large image download fails, try medium version
      if (!downloadSuccess && coverImageUrl.includes('/large/')) {
        const mediumUrl = coverImageUrl.replace('/large/', '/medium/');
        console.log(`Failed to download large cover image, trying medium for ${anilistId}`);
        downloadSuccess = await downloadImage(mediumUrl, tempCoverPath);
        if (downloadSuccess) {
          coverImageUrl = mediumUrl;
        }
      }

      if (downloadSuccess) {
        try {
          coverImageUrl = await uploadToPixeldrain(tempCoverPath);
          fs.unlinkSync(tempCoverPath);
        } catch (error) {
          console.error(`Failed to upload cover image for ${anilistId}:`, error);
        }
      }
    }

    let finalBannerUrl = null;
    if (existingMetadata?.banner_image_manual_override !== true) {
      if (bannerImageUrl?.includes('s4.anilist')) {
        const tempBannerPath = path.join(tempDir, `banner_${anilistId}.jpg`);
        await downloadImage(bannerImageUrl, tempBannerPath);
        try {
          finalBannerUrl = await uploadToPixeldrain(tempBannerPath);
          fs.unlinkSync(tempBannerPath);
        } catch (error) {
          console.error(`Failed to upload banner image for ${anilistId}:`, error);
        }
      } else {
        finalBannerUrl = bannerImageUrl;
      }
    } else {
      finalBannerUrl = existingMetadata.banner_image;
    }

    const formatDate = (date) => {
      if (!date?.year) return null;
      return new Date(Date.UTC(date.year, (date.month || 1) - 1, date.day || 1))
        .toISOString()
        .split('T')[0];
    };

    const { data: existingAnimeIds, error: existingError } = await supabase
      .from('anime_metadata')
      .select('anilist_id');

    if (existingError) {
      console.error(`Error fetching existing anime IDs:`, existingError);
      return metadata;
    }

    const existingIds = new Set(existingAnimeIds.map(a => a.anilist_id));

    const relations = media.relations?.edges || [];
    const animeRelations = relations
      .filter(edge => edge.node.type === 'ANIME' && existingIds.has(edge.node.id))
      .map(edge => ({
        source_id: anilistId,
        target_id: edge.node.id,
        relation_type: edge.relationType
      }));

    if (animeRelations.length > 0) {
      // First, fetch existing relations to check which ones already exist
      const { data: existingRelations, error: fetchRelationsError } = await supabase
        .from('anime_relations')
        .select('source_id, target_id, relation_type')
        .eq('source_id', anilistId);

      if (fetchRelationsError) {
        console.error(`Error fetching existing relations for ID ${anilistId}:`, fetchRelationsError);
      }

      // Create a map of existing relations for efficient lookup
      const existingRelationsMap = new Map();
      if (existingRelations) {
        existingRelations.forEach(relation => {
          const key = `${relation.source_id}-${relation.target_id}`;
          existingRelationsMap.set(key, relation.relation_type);
        });
      }

      // Filter out relations that already exist to prevent overwriting
      const newRelations = animeRelations.filter(relation => {
        const key = `${relation.source_id}-${relation.target_id}`;
        return !existingRelationsMap.has(key);
      });

      // Only insert new relations, don't update existing ones
      if (newRelations.length > 0) {
        const { error: relationsError } = await supabase
          .from('anime_relations')
          .insert(newRelations);

        if (relationsError) {
          console.error(`Error inserting new relations for ID ${anilistId}:`, relationsError);
        } else {
          console.log(`Added ${newRelations.length} new relations for ID ${anilistId}`);
        }
      } else {
        console.log(`No new relations to add for ID ${anilistId}`);
      }
    }

    // First, check if the anime already has synonyms in the database
    const { data: existingFullMetadata, error: fullMetadataError } = await supabase
      .from('anime_metadata')
      .select('synonyms')
      .eq('anilist_id', anilistId)
      .single();

    if (fullMetadataError && fullMetadataError.code !== 'PGRST116') {
      console.error(`Error checking existing full metadata for ID ${anilistId}:`, fullMetadataError);
    }

    // Process synonyms only if they don't already exist
    let synonymsToUse = [];
    if (!existingFullMetadata?.synonyms || existingFullMetadata.synonyms.length === 0) {
      synonymsToUse = (media.synonyms || [])
        .filter(synonym => {
          if (!synonym) return false;
          if (synonym.length > 10) return false;
          if (!isEnglishOnly(synonym)) return false;
          return true;
        })
        .filter(Boolean);
      console.log(`Using ${synonymsToUse.length} new synonyms for ID ${anilistId}`);
    } else {
      synonymsToUse = existingFullMetadata.synonyms;
      console.log(`Preserving ${synonymsToUse.length} existing synonyms for ID ${anilistId}`);
    }

    const metadata = {
      anilist_id: media.id,
      mal_id: media.idMal,
      romaji_title: media.title.romaji,
      english_title: existingMetadata?.english_title || media.title.english,
      native_title: media.title.native,
      synonyms: synonymsToUse,
      cover_image: coverImageUrl,
      ...(finalBannerUrl && { banner_image: finalBannerUrl }),
      format: media.format,
      episodes: media.episodes,
      duration: media.duration,
      source: media.source,
      season: media.season,
      season_year: media.seasonYear,
      start_date: formatDate(media.startDate),
      end_date: formatDate(media.endDate),
      studios: media.studios.nodes.map(studio => studio.name),
      genres: media.genres,
      last_updated: new Date().toISOString()
    };

    return metadata;
  } catch (error) {
    console.error(`Error fetching metadata for ID ${anilistId}:`, error);
    return null;
  }
};

const updateMetadata = async (anilistIds = [], season = null, year = null) => {
  try {
    let idsToProcess = anilistIds;

    // If no IDs provided, fetch from database with optional season/year filtering
    if (idsToProcess.length === 0) {
      let query = supabase
        .from('anime_metadata')
        .select('anilist_id')
        .not('anilist_id', 'is', null)
        .not('anilist_id', 'eq', -1);

      // Apply season and year filters if provided
      if (season && year) {
        query = query
          .eq('season', season.toUpperCase())
          .eq('season_year', parseInt(year));
        console.log(`Filtering by season: ${season.toUpperCase()}, year: ${year}`);
      }

      query = query.order('anilist_id');

      const { data: animeIds, error } = await query;

      if (error) throw error;
      idsToProcess = [...new Set(animeIds.map(a => a.anilist_id))];
    }

    console.log(`Processing ${idsToProcess.length} anime IDs`);
    let counter = 0;

    for (const anilistId of idsToProcess) {
      console.log(`Fetching metadata for AniList ID: ${anilistId}`);

      try {
        const metadata = await fetchAnimeMetadata(anilistId);
        if (metadata) {
          const { error: upsertError } = await supabase
            .from('anime_metadata')
            .upsert(metadata);

          if (upsertError) {
            console.error(`Error upserting metadata for ID ${anilistId}:`, upsertError);
            continue;
          }

          console.log(`[${counter}] Updated metadata for ID ${anilistId}`);
        }

        counter++;
        const delay = getRandomDelay();
        console.log(`Waiting ${delay / 1000} seconds before next request...`);
        await new Promise(resolve => setTimeout(resolve, delay));

      } catch (error) {
        console.error(`Error processing anilist_id ${anilistId}:`, error);
        continue;
      }
    }

  } catch (error) {
    console.error('Error in updateMetadata:', error);
  }
};

// Parse command line arguments
const args = process.argv.slice(2);
let season = null;
let year = null;

// Check if both season and year are provided, or neither
if (args.length === 2) {
  season = args[0];
  year = args[1];

  // Validate season
  const validSeasons = ['WINTER', 'SPRING', 'SUMMER', 'FALL'];
  if (!validSeasons.includes(season.toUpperCase())) {
    console.error(`Invalid season: ${season}. Valid seasons are: ${validSeasons.join(', ')}`);
    process.exit(1);
  }

  // Validate year
  const yearNum = parseInt(year);
  if (isNaN(yearNum) || yearNum < 1900 || yearNum > new Date().getFullYear() + 5) {
    console.error(`Invalid year: ${year}. Year must be a valid number between 1900 and ${new Date().getFullYear() + 5}`);
    process.exit(1);
  }

  console.log(`Running update for ${season.toUpperCase()} ${year}`);
} else if (args.length === 1) {
  console.error('Error: You must provide both season and year arguments, or neither.');
  console.error('Usage: node populate.js [season] [year]');
  console.error('Example: node populate.js WINTER 2024');
  console.error('Valid seasons: WINTER, SPRING, SUMMER, FALL');
  process.exit(1);
} else if (args.length === 0) {
  console.log('Running full update for all anime in database');
} else {
  console.error('Error: Too many arguments provided.');
  console.error('Usage: node populate.js [season] [year]');
  console.error('Example: node populate.js WINTER 2024');
  process.exit(1);
}

// Example usage:
const anilistIds = []; // Leave empty to process all IDs from database, or add specific IDs
updateMetadata(anilistIds, season, year);